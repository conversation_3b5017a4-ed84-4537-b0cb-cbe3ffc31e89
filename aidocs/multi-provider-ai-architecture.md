# Specification: Multi-Provider AI/LLM Architecture for AutoRun-RS

## 1. Goal/Objective

Design and implement a comprehensive multi-provider AI/LLM architecture for AutoRun-RS that supports multiple AI providers with tool/function calling capabilities. The system should be extensible, robust, and follow Rust best practices while providing a unified interface for AI interactions across different providers.

## 2. Input

### 2.1 Reference Analysis

Based on analysis of the Cline implementation in `aidocs/reference/cline/src/`, key architectural patterns identified:

- **Provider Factory Pattern**: Central `buildApiHandler()` function with switch-based provider selection
- **Unified Interface**: Common `ApiHandler` interface with `createMessage()` and `getModel()` methods
- **Stream-based Architecture**: Async generators for streaming responses
- **Configuration Management**: Provider-specific options merged into unified configuration
- **Error Handling**: Provider-specific error handling with retry mechanisms

### 2.2 Current AutoRun-RS State

- Existing `LLMProvider` trait in `src/llm/mod.rs`
- Factory pattern in `src/llm/factory.rs` with `ProviderBuilder` trait
- Configuration system in `src/config/mod.rs` with `LLMConfig`
- Model registry in `src/config/registry.rs`
- Tool calling support detection via `ToolSupport` enum

## 3. Output

### 3.1 Core Architecture Files

- **Enhanced Provider Factory**: `src/llm/factory.rs` (extend existing)
- **Provider Implementations**: `src/llm/providers/` (new directory)
  - `src/llm/providers/mod.rs`
  - `src/llm/providers/openrouter.rs`
  - `src/llm/providers/requesty.rs`
  - `src/llm/providers/anthropic.rs`
  - `src/llm/providers/gemini.rs`
  - `src/llm/providers/openai.rs`
  - `src/llm/providers/ollama.rs`
  - `src/llm/providers/base.rs`
- **Configuration Extensions**: `src/config/providers.rs` (new)
- **Error Handling**: `src/llm/errors.rs` (new)

### 3.2 Configuration Files

- **Provider Configurations**: `config/providers/` (new directory)
- **Model Registry Updates**: Enhanced `src/config/registry.rs`

### 3.3 Documentation

- **Implementation Guide**: This specification document
- **Provider Integration Guide**: `aidocs/provider-integration-guide.md`

## 4. Constraints

- **Tool Calling Requirement**: Only integrate models that support tool/function calling
- **Rust Best Practices**: Follow guidelines in `.claude/code-guidelines/rust.md`
- **Async Architecture**: All providers must support async/await patterns
- **Error Propagation**: Use `Result<T, AutorunError>` pattern consistently
- **No Breaking Changes**: Maintain compatibility with existing `LLMProvider` trait
- **Memory Efficiency**: Use `Arc<dyn Trait>` for shared ownership patterns
- **Configuration Compatibility**: Extend existing TOML configuration format

## 5. Architecture Overview

### 5.1 Design Principles

1. **Unified Interface**: Single `LLMProvider` trait for all providers
2. **Plugin Architecture**: Easy addition of new providers via `ProviderBuilder` trait
3. **Capability Detection**: Runtime validation of tool calling support
4. **Graceful Degradation**: Clear error messages for unsupported features
5. **Configuration Flexibility**: Provider-specific settings with sensible defaults
6. **Async-First**: Non-blocking operations throughout the stack

### 5.2 Core Components

```rust
// Enhanced LLMProvider trait (extend existing)
#[async_trait::async_trait]
pub trait LLMProvider: Send + Sync {
    async fn complete(&self, messages: Vec<Message>) -> Result<String>;
    async fn complete_with_tools(&self, messages: Vec<Message>, tools: Vec<Tool>) -> Result<ToolCallResponse>;
    async fn stream_complete(&self, messages: Vec<Message>) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>>>>>;

    fn supports_tools(&self) -> ToolSupport;
    fn supports_streaming(&self) -> bool;
    fn model_name(&self) -> &str;
    fn provider_name(&self) -> &str;
    fn model_capabilities(&self) -> Vec<ModelCapability>;

    // Provider-specific configuration validation
    fn validate_config(&self, config: &LLMConfig) -> Result<()>;

    // Health check for provider availability
    async fn health_check(&self) -> Result<ProviderHealth>;
}

// Provider factory with enhanced capabilities
pub struct ProviderFactory {
    builders: HashMap<String, Box<dyn ProviderBuilder>>,
}

impl ProviderFactory {
    pub fn new() -> Self {
        let mut factory = Self {
            builders: HashMap::new(),
        };

        // Register all providers
        factory.register("openrouter", Box::new(OpenRouterBuilder));
        factory.register("requesty", Box::new(RequestyBuilder));
        factory.register("anthropic", Box::new(AnthropicBuilder));
        factory.register("gemini", Box::new(GeminiBuilder));
        factory.register("openai", Box::new(OpenAIBuilder));
        factory.register("ollama", Box::new(OllamaBuilder));

        factory
    }

    pub async fn create_provider(&self, config: &LLMConfig) -> Result<Arc<dyn LLMProvider>> {
        let builder = self.builders.get(&config.provider)
            .ok_or_else(|| AutorunError::Config(format!("Unsupported provider: {}", config.provider)))?;

        // Validate configuration before building
        builder.validate_config(config)?;

        // Build provider instance
        let provider = builder.build(config).await?;

        // Validate tool calling support if required
        if config.require_tools.unwrap_or(true) {
            match provider.supports_tools() {
                ToolSupport::None => {
                    return Err(AutorunError::Config(format!(
                        "Provider {} model {} does not support tool calling",
                        config.provider, config.model
                    )));
                }
                ToolSupport::Limited => {
                    log::warn!("Provider {} has limited tool support", config.provider);
                }
                ToolSupport::Full => {}
            }
        }

        Ok(provider)
    }
}
```

### 5.3 Provider Implementation Pattern

Each provider follows a consistent implementation pattern:

```rust
// Base provider trait for common functionality
#[async_trait::async_trait]
pub trait BaseProvider: LLMProvider {
    async fn make_request(&self, request: ProviderRequest) -> Result<ProviderResponse>;
    fn get_client(&self) -> &dyn HttpClient;
    fn get_config(&self) -> &ProviderConfig;

    // Default implementations for common operations
    async fn health_check(&self) -> Result<ProviderHealth> {
        // Default health check implementation
    }

    fn validate_config(&self, config: &LLMConfig) -> Result<()> {
        // Common validation logic
    }
}

// Example provider implementation
pub struct OpenRouterProvider {
    client: reqwest::Client,
    config: OpenRouterConfig,
    model_info: ModelInfo,
}

#[async_trait::async_trait]
impl LLMProvider for OpenRouterProvider {
    async fn complete(&self, messages: Vec<Message>) -> Result<String> {
        let request = self.build_completion_request(messages, None)?;
        let response = self.make_request(request).await?;
        self.parse_completion_response(response)
    }

    async fn complete_with_tools(&self, messages: Vec<Message>, tools: Vec<Tool>) -> Result<ToolCallResponse> {
        if self.supports_tools() == ToolSupport::None {
            return Err(AutorunError::LlmApi("Tool calling not supported".to_string()));
        }

        let request = self.build_completion_request(messages, Some(tools))?;
        let response = self.make_request(request).await?;
        self.parse_tool_response(response)
    }

    fn supports_tools(&self) -> ToolSupport {
        detect_tool_support(&self.config.provider, &self.config.model)
    }

    fn provider_name(&self) -> &str {
        "openrouter"
    }

    fn model_name(&self) -> &str {
        &self.config.model
    }
}
```

## 6. Configuration Management

### 6.1 Enhanced Configuration Structure

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    pub provider: String,
    pub model: String,
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
    pub require_tools: Option<bool>,

    // Provider-specific configurations
    pub openrouter: Option<OpenRouterConfig>,
    pub requesty: Option<RequestyConfig>,
    pub anthropic: Option<AnthropicConfig>,
    pub gemini: Option<GeminiConfig>,
    pub openai: Option<OpenAIConfig>,
    pub ollama: Option<OllamaConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OpenRouterConfig {
    pub site_url: Option<String>,
    pub app_name: Option<String>,
    pub transforms: Option<Vec<String>>,
    pub models: Option<Vec<String>>,
    pub route: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnthropicConfig {
    pub version: Option<String>,
    pub beta_features: Option<Vec<String>>,
    pub max_retries: Option<u32>,
}
```

### 6.2 Configuration File Example

```toml
[llm]
provider = "openrouter"
model = "anthropic/claude-3-5-sonnet-20241022"
api_key = "${OPENROUTER_API_KEY}"
temperature = 0.7
max_tokens = 4096
require_tools = true

[llm.openrouter]
site_url = "https://autorun-rs.dev"
app_name = "AutoRun-RS"
transforms = ["middle-out"]

[llm.anthropic]
version = "2023-06-01"
beta_features = ["tools-2024-04-04"]
max_retries = 3
```

## 7. Error Handling Strategy

### 7.1 Provider-Specific Error Types

```rust
#[derive(Error, Debug)]
pub enum ProviderError {
    #[error("Authentication failed: {0}")]
    Authentication(String),

    #[error("Rate limit exceeded: {retry_after:?}")]
    RateLimit { retry_after: Option<Duration> },

    #[error("Model not found: {model}")]
    ModelNotFound { model: String },

    #[error("Tool calling not supported by model: {model}")]
    ToolsNotSupported { model: String },

    #[error("Invalid request: {0}")]
    InvalidRequest(String),

    #[error("Provider unavailable: {0}")]
    Unavailable(String),

    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),

    #[error("Parsing error: {0}")]
    Parsing(String),
}

// Integration with existing error system
impl From<ProviderError> for AutorunError {
    fn from(err: ProviderError) -> Self {
        AutorunError::LlmApi(err.to_string())
    }
}
```

### 7.2 Retry and Circuit Breaker Pattern

```rust
pub struct RetryConfig {
    pub max_retries: u32,
    pub base_delay: Duration,
    pub max_delay: Duration,
    pub backoff_multiplier: f64,
}

#[async_trait::async_trait]
pub trait RetryableProvider {
    async fn execute_with_retry<T, F, Fut>(&self, operation: F) -> Result<T>
    where
        F: Fn() -> Fut + Send + Sync,
        Fut: Future<Output = Result<T>> + Send,
        T: Send;
}
```

## 8. Tool Calling Implementation

### 8.1 Tool Support Detection

```rust
pub fn detect_tool_support(provider: &str, model: &str) -> ToolSupport {
    match provider {
        "openrouter" => detect_openrouter_tool_support(model),
        "anthropic" => ToolSupport::Full,
        "openai" => detect_openai_tool_support(model),
        "gemini" => detect_gemini_tool_support(model),
        "ollama" => detect_ollama_tool_support(model),
        "requesty" => ToolSupport::Full, // Assume full support
        _ => ToolSupport::None,
    }
}

fn detect_openrouter_tool_support(model: &str) -> ToolSupport {
    // Tool calling support matrix for OpenRouter models
    match model {
        m if m.contains("claude") => ToolSupport::Full,
        m if m.contains("gpt-4") => ToolSupport::Full,
        m if m.contains("gpt-3.5") => ToolSupport::Limited,
        m if m.contains("gemini") => ToolSupport::Full,
        _ => ToolSupport::None,
    }
}
```

### 8.2 Tool Call Response Handling

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCallResponse {
    pub content: String,
    pub tool_calls: Vec<ToolCall>,
    pub finish_reason: Option<String>,
    pub usage: Option<TokenUsage>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    pub id: String,
    pub name: String,
    pub arguments: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}
```

## 9. Implementation Roadmap

### Phase 1: Foundation (Week 1-2)

- [ ] Enhance existing `LLMProvider` trait with new methods
- [ ] Implement base provider infrastructure
- [ ] Create provider-specific error types
- [ ] Set up configuration extensions

### Phase 2: Core Providers (Week 3-4)

- [ ] Implement OpenRouter provider
- [ ] Implement Anthropic provider
- [ ] Implement OpenAI provider
- [ ] Add comprehensive tool calling support

### Phase 3: Extended Providers (Week 5-6)

- [ ] Implement Gemini provider
- [ ] Implement Ollama provider
- [ ] Implement Requesty provider
- [ ] Add streaming support for all providers

### Phase 4: Advanced Features (Week 7-8)

- [ ] Implement retry mechanisms and circuit breakers
- [ ] Add health checking and monitoring
- [ ] Performance optimization and caching
- [ ] Comprehensive testing and documentation

### Phase 5: Integration and Polish (Week 9-10)

- [ ] Integration with existing AutoRun-RS components
- [ ] Configuration migration tools
- [ ] Performance benchmarking
- [ ] Production readiness assessment

## 10. Future Extensibility

### 10.1 Plugin Architecture

The provider system is designed for easy extension:

```rust
// Adding a new provider requires implementing ProviderBuilder
pub struct NewProviderBuilder;

#[async_trait::async_trait]
impl ProviderBuilder for NewProviderBuilder {
    async fn build(&self, config: &LLMConfig) -> Result<Arc<dyn LLMProvider>> {
        // Implementation
    }

    fn validate_config(&self, config: &LLMConfig) -> Result<()> {
        // Validation logic
    }

    fn provider_name(&self) -> &'static str {
        "new_provider"
    }
}

// Register in factory
factory.register("new_provider", Box::new(NewProviderBuilder));
```

### 10.2 Configuration Schema Evolution

- Backward-compatible configuration changes
- Migration tools for configuration updates
- Versioned configuration schemas

### 10.3 Monitoring and Observability

- Provider performance metrics
- Error rate tracking
- Usage analytics
- Health monitoring dashboards

## 11. Provider-Specific Implementation Details

### 11.1 OpenRouter Provider

OpenRouter acts as a meta-provider, supporting multiple underlying models. Key implementation considerations:

```rust
pub struct OpenRouterProvider {
    client: reqwest::Client,
    config: OpenRouterConfig,
    base_url: String,
    api_key: String,
    model: String,
}

impl OpenRouterProvider {
    pub fn new(config: &LLMConfig) -> Result<Self> {
        let openrouter_config = config.openrouter.as_ref()
            .ok_or_else(|| AutorunError::Config("OpenRouter config missing".to_string()))?;

        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(60))
            .default_headers({
                let mut headers = HeaderMap::new();
                headers.insert("Authorization", format!("Bearer {}", config.api_key.as_ref().unwrap()).parse()?);
                headers.insert("HTTP-Referer", openrouter_config.site_url.as_ref().unwrap_or(&"https://autorun-rs.dev".to_string()).parse()?);
                headers.insert("X-Title", openrouter_config.app_name.as_ref().unwrap_or(&"AutoRun-RS".to_string()).parse()?);
                headers
            })
            .build()?;

        Ok(Self {
            client,
            config: openrouter_config.clone(),
            base_url: config.base_url.as_ref().unwrap_or(&"https://openrouter.ai/api/v1".to_string()).clone(),
            api_key: config.api_key.as_ref().unwrap().clone(),
            model: config.model.clone(),
        })
    }

    async fn make_completion_request(&self, messages: Vec<Message>, tools: Option<Vec<Tool>>) -> Result<serde_json::Value> {
        let mut request_body = json!({
            "model": self.model,
            "messages": self.convert_messages(messages)?,
            "temperature": 0.7,
            "max_tokens": 4096,
        });

        if let Some(tools) = tools {
            request_body["tools"] = json!(self.convert_tools(tools)?);
            request_body["tool_choice"] = json!("auto");
        }

        let response = self.client
            .post(&format!("{}/chat/completions", self.base_url))
            .json(&request_body)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(AutorunError::LlmApi(format!("OpenRouter API error: {}", error_text)));
        }

        let response_json: serde_json::Value = response.json().await?;
        Ok(response_json)
    }
}
```

### 11.2 Anthropic Provider

Direct integration with Anthropic's Claude models:

```rust
pub struct AnthropicProvider {
    client: anthropic::Client,
    model: String,
    config: AnthropicConfig,
}

impl AnthropicProvider {
    pub fn new(config: &LLMConfig) -> Result<Self> {
        let anthropic_config = config.anthropic.as_ref()
            .unwrap_or(&AnthropicConfig::default());

        let client = anthropic::Client::builder()
            .api_key(config.api_key.as_ref().unwrap())
            .base_url(config.base_url.as_ref().unwrap_or(&"https://api.anthropic.com".to_string()))
            .build()?;

        Ok(Self {
            client,
            model: config.model.clone(),
            config: anthropic_config.clone(),
        })
    }

    async fn create_message_request(&self, messages: Vec<Message>, tools: Option<Vec<Tool>>) -> Result<anthropic::MessageRequest> {
        let mut request = anthropic::MessageRequest::builder()
            .model(&self.model)
            .max_tokens(4096)
            .messages(self.convert_messages(messages)?)
            .build()?;

        if let Some(tools) = tools {
            request = request.tools(self.convert_tools_to_anthropic(tools)?);
        }

        Ok(request)
    }
}
```

### 11.3 Gemini Provider

Google's Gemini models with specific API patterns:

```rust
pub struct GeminiProvider {
    client: reqwest::Client,
    api_key: String,
    model: String,
    base_url: String,
}

impl GeminiProvider {
    pub fn new(config: &LLMConfig) -> Result<Self> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(60))
            .build()?;

        Ok(Self {
            client,
            api_key: config.api_key.as_ref().unwrap().clone(),
            model: config.model.clone(),
            base_url: config.base_url.as_ref()
                .unwrap_or(&"https://generativelanguage.googleapis.com/v1beta".to_string())
                .clone(),
        })
    }

    async fn generate_content(&self, messages: Vec<Message>, tools: Option<Vec<Tool>>) -> Result<serde_json::Value> {
        let mut request_body = json!({
            "contents": self.convert_messages_to_gemini(messages)?,
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 4096,
            }
        });

        if let Some(tools) = tools {
            request_body["tools"] = json!([{
                "functionDeclarations": self.convert_tools_to_gemini(tools)?
            }]);
        }

        let url = format!("{}/models/{}:generateContent?key={}",
                         self.base_url, self.model, self.api_key);

        let response = self.client
            .post(&url)
            .json(&request_body)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(AutorunError::LlmApi(format!("Gemini API error: {}", error_text)));
        }

        let response_json: serde_json::Value = response.json().await?;
        Ok(response_json)
    }
}
```

### 11.4 Ollama Provider

Local model hosting with Ollama:

```rust
pub struct OllamaProvider {
    client: reqwest::Client,
    base_url: String,
    model: String,
}

impl OllamaProvider {
    pub fn new(config: &LLMConfig) -> Result<Self> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(120)) // Longer timeout for local models
            .build()?;

        Ok(Self {
            client,
            base_url: config.base_url.as_ref()
                .unwrap_or(&"http://localhost:11434".to_string())
                .clone(),
            model: config.model.clone(),
        })
    }

    async fn health_check(&self) -> Result<ProviderHealth> {
        let response = self.client
            .get(&format!("{}/api/tags", self.base_url))
            .send()
            .await?;

        if response.status().is_success() {
            let models: serde_json::Value = response.json().await?;
            let available_models: Vec<String> = models["models"]
                .as_array()
                .unwrap_or(&vec![])
                .iter()
                .filter_map(|m| m["name"].as_str().map(|s| s.to_string()))
                .collect();

            if available_models.contains(&self.model) {
                Ok(ProviderHealth::Healthy)
            } else {
                Ok(ProviderHealth::Degraded(format!("Model {} not available", self.model)))
            }
        } else {
            Ok(ProviderHealth::Unhealthy("Ollama server not responding".to_string()))
        }
    }
}
```

## 12. Streaming Implementation

### 12.1 Unified Streaming Interface

```rust
#[async_trait::async_trait]
pub trait StreamingProvider: LLMProvider {
    async fn stream_complete(&self, messages: Vec<Message>) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>>>>>;
    async fn stream_complete_with_tools(&self, messages: Vec<Message>, tools: Vec<Tool>) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>>>>>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StreamChunk {
    Content { delta: String },
    ToolCall { id: String, name: String, arguments_delta: String },
    ToolCallComplete { id: String, name: String, arguments: serde_json::Value },
    Usage { tokens: TokenUsage },
    Done,
}

// Example streaming implementation for OpenRouter
impl StreamingProvider for OpenRouterProvider {
    async fn stream_complete(&self, messages: Vec<Message>) -> Result<Pin<Box<dyn Stream<Item = Result<StreamChunk>>>>> {
        let request_body = json!({
            "model": self.model,
            "messages": self.convert_messages(messages)?,
            "stream": true,
            "temperature": 0.7,
        });

        let response = self.client
            .post(&format!("{}/chat/completions", self.base_url))
            .json(&request_body)
            .send()
            .await?;

        let stream = response.bytes_stream()
            .map_err(|e| AutorunError::LlmApi(e.to_string()))
            .and_then(|chunk| async move {
                self.parse_sse_chunk(chunk).await
            });

        Ok(Box::pin(stream))
    }
}
```

### 12.2 Server-Sent Events Parsing

```rust
impl OpenRouterProvider {
    async fn parse_sse_chunk(&self, chunk: bytes::Bytes) -> Result<StreamChunk> {
        let chunk_str = std::str::from_utf8(&chunk)?;

        for line in chunk_str.lines() {
            if line.starts_with("data: ") {
                let data = &line[6..];

                if data == "[DONE]" {
                    return Ok(StreamChunk::Done);
                }

                let parsed: serde_json::Value = serde_json::from_str(data)?;

                if let Some(choices) = parsed["choices"].as_array() {
                    if let Some(choice) = choices.first() {
                        if let Some(delta) = choice["delta"].as_object() {
                            if let Some(content) = delta["content"].as_str() {
                                return Ok(StreamChunk::Content {
                                    delta: content.to_string()
                                });
                            }

                            if let Some(tool_calls) = delta["tool_calls"].as_array() {
                                // Handle tool call streaming
                                return self.parse_tool_call_delta(tool_calls);
                            }
                        }
                    }
                }
            }
        }

        Err(AutorunError::LlmApi("Invalid SSE chunk".to_string()))
    }
}
```

## 13. Testing Strategy

### 13.1 Unit Tests

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    #[tokio::test]
    async fn test_openrouter_provider_creation() {
        let config = LLMConfig {
            provider: "openrouter".to_string(),
            model: "anthropic/claude-3-5-sonnet-20241022".to_string(),
            api_key: Some("test-key".to_string()),
            base_url: Some("https://openrouter.ai/api/v1".to_string()),
            openrouter: Some(OpenRouterConfig {
                site_url: Some("https://test.com".to_string()),
                app_name: Some("Test App".to_string()),
                ..Default::default()
            }),
            ..Default::default()
        };

        let provider = OpenRouterProvider::new(&config).unwrap();
        assert_eq!(provider.provider_name(), "openrouter");
        assert_eq!(provider.model_name(), "anthropic/claude-3-5-sonnet-20241022");
        assert!(provider.supports_tools() != ToolSupport::None);
    }

    #[tokio::test]
    async fn test_tool_support_detection() {
        assert_eq!(detect_tool_support("openrouter", "anthropic/claude-3-5-sonnet-20241022"), ToolSupport::Full);
        assert_eq!(detect_tool_support("openrouter", "meta-llama/llama-2-7b-chat"), ToolSupport::None);
        assert_eq!(detect_tool_support("anthropic", "claude-3-5-sonnet-20241022"), ToolSupport::Full);
    }

    #[tokio::test]
    async fn test_provider_factory() {
        let factory = ProviderFactory::new();

        let config = LLMConfig {
            provider: "anthropic".to_string(),
            model: "claude-3-5-sonnet-20241022".to_string(),
            api_key: Some("test-key".to_string()),
            ..Default::default()
        };

        let provider = factory.create_provider(&config).await.unwrap();
        assert_eq!(provider.provider_name(), "anthropic");
    }
}
```

### 13.2 Integration Tests

```rust
#[cfg(test)]
mod integration_tests {
    use super::*;

    #[tokio::test]
    #[ignore] // Requires API keys
    async fn test_real_openrouter_completion() {
        let config = LLMConfig {
            provider: "openrouter".to_string(),
            model: "anthropic/claude-3-5-sonnet-20241022".to_string(),
            api_key: std::env::var("OPENROUTER_API_KEY").ok(),
            ..Default::default()
        };

        if config.api_key.is_none() {
            return; // Skip test if no API key
        }

        let provider = OpenRouterProvider::new(&config).unwrap();
        let messages = vec![Message::user("Hello, how are you?")];

        let response = provider.complete(messages).await.unwrap();
        assert!(!response.is_empty());
    }

    #[tokio::test]
    #[ignore] // Requires API keys
    async fn test_tool_calling_integration() {
        // Test actual tool calling with real providers
    }
}
```

## 14. Implementation Guidelines

### 14.1 Development Workflow

1. **Start with Base Infrastructure**

   - Implement enhanced `LLMProvider` trait
   - Create base provider infrastructure
   - Set up error handling and configuration extensions

2. **Implement One Provider at a Time**

   - Begin with OpenRouter (most versatile)
   - Add comprehensive tests for each provider
   - Validate tool calling capabilities

3. **Follow Rust Best Practices**

   - Use `#![deny(clippy::unwrap_used)]` to enforce proper error handling
   - Implement `From` traits for error conversions
   - Use `Arc<dyn Trait>` for shared ownership
   - Prefer borrowing over cloning

4. **Testing Strategy**
   - Unit tests for each provider
   - Integration tests with real APIs (behind feature flags)
   - Mock tests for error scenarios
   - Performance benchmarks

### 14.2 Code Organization

```
src/llm/
├── mod.rs                 # Main module with traits and factory
├── factory.rs             # Enhanced provider factory
├── errors.rs              # Provider-specific errors
├── streaming.rs           # Streaming utilities
├── providers/
│   ├── mod.rs            # Provider module exports
│   ├── base.rs           # Base provider trait and utilities
│   ├── openrouter.rs     # OpenRouter implementation
│   ├── anthropic.rs      # Anthropic implementation
│   ├── gemini.rs         # Gemini implementation
│   ├── openai.rs         # OpenAI implementation
│   ├── ollama.rs         # Ollama implementation
│   └── requesty.rs       # Requesty implementation
└── utils/
    ├── mod.rs            # Utility module exports
    ├── tool_detection.rs # Tool support detection
    ├── message_conversion.rs # Message format conversion
    └── retry.rs          # Retry mechanisms
```

### 14.3 Configuration Best Practices

1. **Environment Variable Support**

   ```rust
   pub fn load_api_key(provider: &str, config_key: Option<&str>) -> Option<String> {
       config_key.map(|k| k.to_string())
           .or_else(|| std::env::var(format!("{}_API_KEY", provider.to_uppercase())).ok())
           .or_else(|| std::env::var("LLM_API_KEY").ok())
   }
   ```

2. **Configuration Validation**

   ```rust
   impl LLMConfig {
       pub fn validate(&self) -> Result<()> {
           if self.provider.is_empty() {
               return Err(AutorunError::Config("Provider cannot be empty".to_string()));
           }

           if self.model.is_empty() {
               return Err(AutorunError::Config("Model cannot be empty".to_string()));
           }

           // Provider-specific validation
           match self.provider.as_str() {
               "openrouter" => self.validate_openrouter_config(),
               "anthropic" => self.validate_anthropic_config(),
               "gemini" => self.validate_gemini_config(),
               _ => Ok(()),
           }
       }
   }
   ```

3. **Secure API Key Handling**

   ```rust
   use secrecy::{Secret, ExposeSecret};

   #[derive(Debug, Clone)]
   pub struct SecureConfig {
       pub provider: String,
       pub model: String,
       pub api_key: Option<Secret<String>>,
       // ... other fields
   }

   impl SecureConfig {
       pub fn get_api_key(&self) -> Option<&str> {
           self.api_key.as_ref().map(|k| k.expose_secret())
       }
   }
   ```

### 14.4 Error Handling Patterns

1. **Provider-Specific Error Mapping**

   ```rust
   impl From<reqwest::Error> for ProviderError {
       fn from(err: reqwest::Error) -> Self {
           if err.is_timeout() {
               ProviderError::Timeout
           } else if err.is_connect() {
               ProviderError::ConnectionFailed(err.to_string())
           } else {
               ProviderError::Network(err)
           }
       }
   }
   ```

2. **Retry Logic with Exponential Backoff**

   ```rust
   pub async fn retry_with_backoff<T, F, Fut>(
       operation: F,
       max_retries: u32,
       base_delay: Duration,
   ) -> Result<T>
   where
       F: Fn() -> Fut,
       Fut: Future<Output = Result<T>>,
   {
       let mut delay = base_delay;

       for attempt in 0..max_retries {
           match operation().await {
               Ok(result) => return Ok(result),
               Err(err) if attempt == max_retries - 1 => return Err(err),
               Err(ProviderError::RateLimit { retry_after }) => {
                   let wait_time = retry_after.unwrap_or(delay);
                   tokio::time::sleep(wait_time).await;
                   delay = std::cmp::min(delay * 2, Duration::from_secs(60));
               }
               Err(_) => {
                   tokio::time::sleep(delay).await;
                   delay = std::cmp::min(delay * 2, Duration::from_secs(60));
               }
           }
       }

       unreachable!()
   }
   ```

### 14.5 Performance Considerations

1. **Connection Pooling**

   ```rust
   pub fn create_http_client() -> reqwest::Client {
       reqwest::Client::builder()
           .pool_max_idle_per_host(10)
           .pool_idle_timeout(Duration::from_secs(30))
           .timeout(Duration::from_secs(60))
           .build()
           .expect("Failed to create HTTP client")
   }
   ```

2. **Async Streaming**

   ```rust
   use futures::stream::{Stream, StreamExt};
   use tokio::sync::mpsc;

   pub async fn create_streaming_response(
       response: reqwest::Response,
   ) -> Result<impl Stream<Item = Result<StreamChunk>>> {
       let (tx, rx) = mpsc::unbounded_channel();

       tokio::spawn(async move {
           let mut stream = response.bytes_stream();
           while let Some(chunk) = stream.next().await {
               match chunk {
                   Ok(bytes) => {
                       if let Ok(chunk) = parse_sse_chunk(bytes) {
                           if tx.send(Ok(chunk)).is_err() {
                               break;
                           }
                       }
                   }
                   Err(e) => {
                       let _ = tx.send(Err(AutorunError::LlmApi(e.to_string())));
                       break;
                   }
               }
           }
       });

       Ok(tokio_stream::wrappers::UnboundedReceiverStream::new(rx))
   }
   ```

### 14.6 Security Considerations

1. **API Key Protection**

   - Never log API keys
   - Use secure storage for configuration
   - Support environment variable injection
   - Implement key rotation capabilities

2. **Request Validation**

   - Validate all input parameters
   - Sanitize user-provided content
   - Implement request size limits
   - Add timeout protections

3. **Response Handling**
   - Validate response formats
   - Handle malformed responses gracefully
   - Implement content filtering if needed
   - Log security-relevant events

### 14.7 Monitoring and Observability

1. **Metrics Collection**

   ```rust
   use prometheus::{Counter, Histogram, register_counter, register_histogram};

   lazy_static! {
       static ref REQUEST_COUNTER: Counter = register_counter!(
           "llm_requests_total",
           "Total number of LLM requests"
       ).unwrap();

       static ref REQUEST_DURATION: Histogram = register_histogram!(
           "llm_request_duration_seconds",
           "Duration of LLM requests"
       ).unwrap();
   }

   pub async fn make_request_with_metrics<T, F, Fut>(operation: F) -> Result<T>
   where
       F: FnOnce() -> Fut,
       Fut: Future<Output = Result<T>>,
   {
       REQUEST_COUNTER.inc();
       let timer = REQUEST_DURATION.start_timer();

       let result = operation().await;
       timer.observe_duration();

       result
   }
   ```

2. **Structured Logging**

   ```rust
   use tracing::{info, warn, error, instrument};

   #[instrument(skip(self, messages))]
   pub async fn complete(&self, messages: Vec<Message>) -> Result<String> {
       info!(
           provider = self.provider_name(),
           model = self.model_name(),
           message_count = messages.len(),
           "Starting completion request"
       );

       match self.make_request(messages).await {
           Ok(response) => {
               info!("Completion request successful");
               Ok(response)
           }
           Err(e) => {
               error!(error = %e, "Completion request failed");
               Err(e)
           }
       }
   }
   ```

## 15. Migration Strategy

### 15.1 Backward Compatibility

The new multi-provider architecture maintains full backward compatibility with existing code:

```rust
// Existing code continues to work
let config = LLMConfig {
    provider: "openrouter".to_string(),
    model: "anthropic/claude-3-5-sonnet-20241022".to_string(),
    api_key: Some("key".to_string()),
    // ... existing fields
};

let provider = create_llm_provider(&config).await?;
let response = provider.complete(messages).await?;
```

### 15.2 Configuration Migration

1. **Automatic Migration**

   ```rust
   impl LLMConfig {
       pub fn migrate_from_v1(old_config: &OldLLMConfig) -> Self {
           Self {
               provider: old_config.provider.clone(),
               model: old_config.model.clone(),
               api_key: old_config.api_key.clone(),
               base_url: old_config.base_url.clone(),
               temperature: old_config.temperature,
               max_tokens: old_config.max_tokens,
               require_tools: Some(true), // Default to requiring tools
               // Initialize provider-specific configs as None
               openrouter: None,
               anthropic: None,
               gemini: None,
               openai: None,
               ollama: None,
               requesty: None,
           }
       }
   }
   ```

2. **Configuration Validation and Upgrade**

   ```rust
   pub async fn load_and_migrate_config(path: &Path) -> Result<LLMConfig> {
       let content = tokio::fs::read_to_string(path).await?;

       // Try to parse as new format first
       if let Ok(config) = toml::from_str::<LLMConfig>(&content) {
           config.validate()?;
           return Ok(config);
       }

       // Fall back to old format and migrate
       let old_config: OldLLMConfig = toml::from_str(&content)?;
       let new_config = LLMConfig::migrate_from_v1(&old_config);
       new_config.validate()?;

       // Optionally save migrated config
       let new_content = toml::to_string_pretty(&new_config)?;
       tokio::fs::write(path, new_content).await?;

       Ok(new_config)
   }
   ```

## 16. Conclusion

This specification provides a comprehensive foundation for implementing a robust, extensible multi-provider AI architecture in AutoRun-RS. The design emphasizes:

- **Extensibility**: Easy addition of new providers through the plugin architecture
- **Reliability**: Comprehensive error handling and retry mechanisms
- **Performance**: Async-first design with streaming support
- **Security**: Secure API key handling and request validation
- **Maintainability**: Clear separation of concerns and consistent patterns
- **Compatibility**: Backward compatibility with existing code

The implementation should be done incrementally, starting with the base infrastructure and adding providers one by one, with comprehensive testing at each step. This approach ensures a stable, production-ready multi-provider system that can grow with the project's needs.
