use crate::agent::core::ToolExecutor;
use crate::agent::core::{Agent<PERSON><PERSON>, AgentUpdate};
use crate::commands::{CommandRegistry, CommandResponse, ScrollDirection, UiAction};
use crate::config::{Config, LLMConfig};
use crate::core::{AppCore, AppCoreMessage};
use crate::errors::Result;
use crate::tools::{ExecutionContext, ToolRegistry};
use crate::mcp::client::McpClientManager;
use crate::ui::enhanced::{EnhancedTuiInterface, TuiConfig, Action as TuiAction};
use crate::ui::completion::{CompletionEngine, CompletionPopup, MentionCompletionEngine, MentionPopupState};
use crate::ui::widgets::{WidgetFactory, WidgetRegistry};
use serde_json::Value;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use tokio::task::JoinHandle;
use tracing::{error, info};
use tui_textarea::TextArea;

// Re-export UI types for compatibility  
pub use crate::ui::enhanced::Mode;

// Re-export AppMessage for compatibility
pub type AppMessage = AppCoreMessage;

pub struct App {
    // Service container for dependency injection
    pub service_container: crate::core::ServiceContainer,
    config_path: Option<PathBuf>,
    workdir: Option<PathBuf>,
    // Enhanced TUI interface
    pub enhanced_tui: EnhancedTuiInterface,
    // Legacy compatibility fields (will be gradually removed)
    pub agent_tx: mpsc::Sender<AppMessage>,
    pub agent_rx: mpsc::Receiver<AppMessage>,
    pub agent_core: Option<AgentCore>,
    pub command_registry: Arc<CommandRegistry>,
    // Widget system (will be integrated into enhanced TUI)
    pub widget_factory: Arc<WidgetFactory>,
    pub widget_registry: Arc<WidgetRegistry>,
    pub active_widgets: Vec<String>,
}

impl App {
    /// Create an AgentCore with proper tool support detection using provider factory
    pub fn create_agent_core_with_tool_support(
        llm_provider: Arc<dyn crate::llm::LLMProvider>,
        tool_executor: Arc<ToolExecutor>,
        execution_context: ExecutionContext,
        _provider_name: &str,
    ) -> AgentCore {
        use crate::agent::AgentCoreFactory;
        
        // Use the centralized factory to create AgentCore with tool support
        let factory = AgentCoreFactory::new();
        factory.create_agent_core_from_provider(llm_provider, tool_executor, execution_context)
    }

    pub async fn new(config_path: Option<PathBuf>, workdir: Option<PathBuf>) -> Result<Self> {
        Self::new_with_options(config_path, workdir, false).await
    }

    pub async fn new_with_options(config_path: Option<PathBuf>, workdir: Option<PathBuf>, show_logs: bool) -> Result<Self> {
        info!("Initializing AutoRun application with Enhanced TUI");

        // Create service container with dependency injection
        let service_container = crate::core::ServiceContainer::new_with_paths(
            config_path.as_deref(), 
            workdir.as_deref()
        ).await?;
        
        // Start up services
        service_container.startup().await?;

        // Get services from container
        let config = service_container.get_config();
        let provider_factory = service_container.get_provider_factory();
        let tool_executor = service_container.get_tool_executor();
        let tool_registry = service_container.get_tool_registry();

        // Create LLM provider using factory
        let llm_provider = provider_factory.create_provider(&config.llm).await?;
        let provider_name = config.llm.provider.clone();

        // Create execution context with permissions
        let working_dir = workdir
            .as_ref()
            .map(|p| p.clone())
            .unwrap_or_else(|| std::env::current_dir().unwrap());
        let execution_context =
            ExecutionContext::with_config(working_dir, config.clone(), uuid::Uuid::new_v4().to_string())
                .with_permissions(crate::tools::context::Permission {
                    read: true,
                    write: true,
                    execute: false,
                    network: false,
                    system: false,
                });

        // Create agent core with proper tool support detection
        let agent_core = Self::create_agent_core_with_tool_support(
            llm_provider,
            tool_executor.clone(),
            execution_context,
            &provider_name,
        );

        // Create TUI configuration from app config
        let mut tui_config = TuiConfig::default();
        tui_config.ui.vim_mode = config.ui.vim_mode;
        tui_config.ui.show_line_numbers = config.ui.show_line_numbers;
        tui_config.ui.auto_completion = true;
        tui_config.ui.syntax_highlighting = true;

        // Create message channels for TUI-Agent communication
        let (tui_to_agent_tx, tui_to_agent_rx) = mpsc::channel(100);
        let (agent_to_tui_tx, agent_to_tui_rx) = mpsc::channel(100);

        // Create enhanced TUI interface with shared message channels
        let mut enhanced_tui = EnhancedTuiInterface::new_with_channels(
            tui_config,
            config.ui.vim_mode,
            false, // accessibility 
            false, // debug_mode
            show_logs,
            Some((tui_to_agent_tx.clone(), agent_to_tui_rx)),
        ).await?;

        let command_registry = Arc::new(CommandRegistry::new());
        
        // Initialize widget system
        let widget_factory = Arc::new(WidgetFactory::new());
        
        // Register all widget builders
        widget_factory.register_builder(
            "checkbox".to_string(),
            crate::ui::widgets::checkbox::CheckboxWidgetBuilder,
        );
        widget_factory.register_builder(
            "enhanced_list".to_string(),
            crate::ui::widgets::enhanced_list::EnhancedListWidgetBuilder,
        );
        widget_factory.register_builder(
            "options".to_string(),
            crate::ui::widgets::options::OptionsWidgetBuilder,
        );
        
        let widget_registry = Arc::new(WidgetRegistry::new(widget_factory.clone()));

        Ok(Self {
            service_container,
            config_path,
            workdir,
            enhanced_tui,
            agent_tx: tui_to_agent_tx,
            agent_rx: tui_to_agent_rx,
            agent_core: Some(agent_core),
            command_registry,
            widget_factory,
            widget_registry,
            active_widgets: Vec::new(),
        })
    }

    /// Override the LLM configuration for this app instance
    pub fn override_llm_config(&mut self, _llm_config: LLMConfig) -> Result<()> {
        // We need to recreate the AgentCore with the new configuration
        // This is necessary because the LLM provider is created during initialization
        if let Some(agent_core) = self.agent_core.take() {
            // We cannot easily update the existing AgentCore's LLM provider
            // So we'll need to implement this differently
            // For now, we'll store the override config and recreate on next use
            tracing::warn!(
                "LLM config override requested, but AgentCore recreation not yet implemented"
            );
            self.agent_core = Some(agent_core);
        }
        Ok(())
    }

    /// Create a new App with custom LLM configuration
    pub async fn new_with_llm_override(
        config_path: Option<PathBuf>,
        workdir: Option<PathBuf>,
        llm_override: LLMConfig,
    ) -> Result<Self> {
        Self::new_with_llm_override_and_options(config_path, workdir, llm_override, false).await
    }

    /// Create a new App with custom LLM configuration and log panel option
    pub async fn new_with_llm_override_and_options(
        config_path: Option<PathBuf>,
        workdir: Option<PathBuf>,
        llm_override: LLMConfig,
        show_logs: bool,
    ) -> Result<Self> {
        info!("Initializing AutoRun application with Enhanced TUI and LLM override");

        // Create service container and apply LLM override
        let mut service_container = crate::core::ServiceContainer::new_with_paths(
            config_path.as_deref(), 
            workdir.as_deref()
        ).await?;
        
        // Override LLM configuration in the service container
        let mut config = service_container.get_config().clone();
        config.llm = llm_override;
        service_container.update_config(config);
        
        // Start up services
        service_container.startup().await?;

        // Get services from container
        let config = service_container.get_config();
        let provider_factory = service_container.get_provider_factory();
        let tool_executor = service_container.get_tool_executor();
        let tool_registry = service_container.get_tool_registry();

        // Create LLM provider using factory
        let llm_provider = provider_factory.create_provider(&config.llm).await?;

        // Capture configuration info
        let config_info = if !config.llm.has_api_key() {
            vec![
                "".to_string(),
                "Warning: No API key detected. Set one of:".to_string(),
                "- ANTHROPIC_API_KEY for Anthropic Claude".to_string(),
                "- OPENROUTER_API_KEY for OpenRouter models".to_string(),
            ]
        } else {
            vec![format!(
                "Using {} provider with model: {} (base_url: {})",
                config.llm.provider,
                config.llm.model,
                config.llm.effective_base_url()
            )]
        };

        // Store vim_mode and provider
        let vim_mode = config.ui.vim_mode;
        let provider_name = config.llm.provider.clone();

        // Create execution context with permissions
        let working_dir = workdir
            .as_ref()
            .map(|p| p.clone())
            .unwrap_or_else(|| std::env::current_dir().unwrap());
        let execution_context =
            ExecutionContext::with_config(working_dir, config.clone(), uuid::Uuid::new_v4().to_string())
                .with_permissions(crate::tools::context::Permission {
                    read: true,
                    write: true,
                    execute: false,
                    network: false,
                    system: false,
                });

        // Create agent core with proper tool support detection
        let agent_core = Self::create_agent_core_with_tool_support(
            llm_provider,
            tool_executor,
            execution_context,
            &provider_name,
        );

        // Create TUI configuration from app config
        let mut tui_config = TuiConfig::default();
        tui_config.ui.vim_mode = config.ui.vim_mode;
        tui_config.ui.show_line_numbers = config.ui.show_line_numbers;
        tui_config.ui.auto_completion = true;
        tui_config.ui.syntax_highlighting = true;

        // Create message channels for TUI-Agent communication
        let (tui_to_agent_tx, tui_to_agent_rx) = mpsc::channel(100);
        let (agent_to_tui_tx, agent_to_tui_rx) = mpsc::channel(100);

        // Create enhanced TUI interface with shared message channels
        let mut enhanced_tui = EnhancedTuiInterface::new_with_channels(
            tui_config,
            config.ui.vim_mode,
            false, // accessibility 
            false, // debug_mode
            show_logs,
            Some((tui_to_agent_tx.clone(), agent_to_tui_rx)),
        ).await?;

        let command_registry = Arc::new(CommandRegistry::new());
        
        // Initialize widget system
        let widget_factory = Arc::new(WidgetFactory::new());
        
        // Register all widget builders
        widget_factory.register_builder(
            "checkbox".to_string(),
            crate::ui::widgets::checkbox::CheckboxWidgetBuilder,
        );
        widget_factory.register_builder(
            "enhanced_list".to_string(),
            crate::ui::widgets::enhanced_list::EnhancedListWidgetBuilder,
        );
        widget_factory.register_builder(
            "options".to_string(),
            crate::ui::widgets::options::OptionsWidgetBuilder,
        );
        
        let widget_registry = Arc::new(WidgetRegistry::new(widget_factory.clone()));

        Ok(Self {
            service_container,
            config_path,
            workdir,
            enhanced_tui,
            agent_tx: tui_to_agent_tx,
            agent_rx: tui_to_agent_rx,
            agent_core: Some(agent_core),
            command_registry,
            widget_factory,
            widget_registry,
            active_widgets: Vec::new(),
        })
    }

    pub async fn run(&mut self) -> Result<()> {
        info!("Running AutoRun application");

        // Setup terminal
        let mut terminal = crate::ui::setup_terminal()?;

        // Spawn agent background task
        let agent_handle = self.spawn_agent_task();

        // Run the Enhanced TUI event loop
        let result = self.run_enhanced_tui(&mut terminal).await;

        // Clean up agent task
        agent_handle.abort();

        // Restore terminal
        crate::ui::restore_terminal(&mut terminal)?;

        result
    }

    async fn run_enhanced_tui<B: ratatui::backend::Backend>(
        &mut self,
        terminal: &mut ratatui::Terminal<B>,
    ) -> Result<()> {
        use crossterm::event;
        use std::time::Duration;

        loop {
            // Process any pending messages
            self.enhanced_tui.process_messages().await?;

            // Render the enhanced TUI
            if let Err(e) = terminal.draw(|f| {
                tokio::task::block_in_place(|| {
                    tokio::runtime::Handle::current().block_on(async {
                        if let Err(e) = self.enhanced_tui.render(f).await {
                            eprintln!("Render error: {}", e);
                        }
                    })
                });
            }) {
                error!("Failed to draw UI: {}", e);
                // Try to continue instead of breaking the loop
                continue;
            }

            // Handle events with timeout to allow checking for agent messages
            if event::poll(Duration::from_millis(100))? {
                let event = event::read()?;
                
                // Handle the event with the enhanced TUI
                if let Some(action) = self.enhanced_tui.handle_event(event).await? {
                    match action {
                        TuiAction::System(crate::ui::enhanced::SystemAction::Exit) => break,
                        // Enhanced TUI handles input internally
                        _ => {
                            // Handle other actions as needed
                        }
                    }
                }
            }

            // Check for agent responses
            while let Ok(msg) = self.agent_rx.try_recv() {
                match msg {
                    AppMessage::AgentResponse(response) => {
                        // Add response to the enhanced TUI's content navigator
                        // This would need to be implemented in the enhanced TUI
                        info!("Agent response: {}", response);
                    }
                    AppMessage::ProcessingStatus(status) => {
                        info!("Processing status: {}", status);
                    }
                    AppMessage::Error(error) => {
                        error!("Agent error: {}", error);
                    }
                    _ => {}
                }
            }
        }

        Ok(())
    }

    // Enhanced TUI handles UI events directly, no additional event loop needed here

    fn spawn_agent_task(&mut self) -> JoinHandle<()> {
        // The agent will receive messages from the TUI via the agent_rx
        // and send responses back via a direct connection to the TUI's message system
        // We need to create a temporary receiver to replace agent_rx
        let (temp_tx, temp_rx) = mpsc::channel(1);
        let mut ui_to_agent_rx = std::mem::replace(&mut self.agent_rx, temp_rx);
        let (agent_to_ui_tx, mut agent_to_ui_rx) = mpsc::channel::<AgentUpdate>(100);

        // Take ownership of agent_core temporarily
        let mut agent_core = self.agent_core.take().expect("Agent core should exist");

        // Get the Enhanced TUI's message sender for sending responses back to the UI
        let ui_tx = self.enhanced_tui.get_message_sender();

        // Set up the agent's UI update channel
        agent_core.set_ui_update_channel(agent_to_ui_tx);

        // Spawn the agent task
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    // Handle messages from UI
                    Some(msg) = ui_to_agent_rx.recv() => {
                        match msg {
                            crate::core::AppCoreMessage::UserInput(input) => {
                                info!("Processing user input in agent task: {}", input);
                                // Send processing update
                                let _ = ui_tx.send(crate::core::AppCoreMessage::ProcessingStatus("Processing request...".to_string())).await;

                                // Add user message to conversation history
                                if let Err(e) = agent_core.add_user_message(input).await {
                                    error!("Failed to add user message: {}", e);
                                    let _ = ui_tx.send(crate::core::AppCoreMessage::Error(format!("Failed to process message: {}", e))).await;
                                    continue;
                                }

                                // Update status
                                let _ = ui_tx.send(crate::core::AppCoreMessage::ProcessingStatus("Generating response...".to_string())).await;

                                // Run the agent loop to get response
                                if let Err(e) = agent_core.agent_loop().await {
                                    error!("Agent loop error: {}", e);

                                    // Provide more helpful error messages based on error type
                                    let error_msg = match &e {
                                        crate::errors::AutorunError::Config(msg) if msg.contains("API key") => {
                                            format!("Configuration Error: {}

Please set your API key:
- For Anthropic: export ANTHROPIC_API_KEY=\"your-key-here\"
- For OpenRouter: export OPENROUTER_API_KEY=\"your-key-here\"

Then restart the application.", msg)
                                        }
                                        crate::errors::AutorunError::LlmApi(msg) if msg.contains("405") => {
                                            "API Error: Incorrect API endpoint. This might be a configuration issue.".to_string()
                                        }
                                        _ => format!("Agent error: {}", e)
                                    };

                                    let _ = ui_tx.send(crate::core::AppCoreMessage::Error(error_msg)).await;
                                }
                            }
                            crate::core::AppCoreMessage::Exit => break,
                            _ => {}
                        }
                    }

                    // Handle updates from agent to UI
                    Some(update) = agent_to_ui_rx.recv() => {
                        match update {
                            AgentUpdate::LlmText(text) => {
                                info!("LLM response text: {}", text);
                                let _ = ui_tx.send(crate::core::AppCoreMessage::AgentResponse(text)).await;
                            }
                            AgentUpdate::Error(err) => {
                                let _ = ui_tx.send(crate::core::AppCoreMessage::Error(err)).await;
                            }
                            AgentUpdate::ToolCallRequested { tool_name, call_id } => {
                                let _ = ui_tx.send(crate::core::AppCoreMessage::AgentResponse(format!("🔧 Requested tool: {} ({})", tool_name, call_id))).await;
                            }
                            AgentUpdate::ToolExecutionStarted { tool_name, call_id } => {
                                let _ = ui_tx.send(crate::core::AppCoreMessage::AgentResponse(format!("⚡ Executing tool: {} ({})", tool_name, call_id))).await;
                            }
                            AgentUpdate::ToolResult(result) => {
                                match result {
                                    crate::agent::core::ToolExecutionResult::Success { output } => {
                                        let formatted_output = if let Some(pretty) = format_tool_output(&output) {
                                            pretty
                                        } else {
                                            format!("{:?}", output)
                                        };
                                        let _ = ui_tx.send(crate::core::AppCoreMessage::AgentResponse(format!("✅ Tool completed: {}", formatted_output))).await;
                                    }
                                    crate::agent::core::ToolExecutionResult::Failure { 
                                        error_message,
                                        error_code,
                                        context,
                                        retry_suggested,
                                    } => {
                                        // Use enhanced error information for better user experience
                                        let error_display = if let Some(ctx) = context {
                                            format!(
                                                "❌ Tool '{}' failed ({}): {}{}{}",
                                                ctx.tool_name,
                                                ctx.execution_type,
                                                error_message,
                                                if let Some(code) = error_code { format!(" [{}]", code) } else { String::new() },
                                                if retry_suggested { " (retryable)" } else { "" }
                                            )
                                        } else {
                                            format!("❌ Tool failed: {}", error_message)
                                        };
                                        
                                        let _ = ui_tx.send(crate::core::AppCoreMessage::Error(error_display)).await;
                                    }
                                }
                            }
                            AgentUpdate::ToolPermissionDenied { tool_name, call_id } => {
                                let _ = ui_tx.send(crate::core::AppCoreMessage::Error(format!("🚫 Permission denied for tool: {} ({})", tool_name, call_id))).await;
                            }
                            AgentUpdate::LlmThinking(thinking) => {
                                let _ = ui_tx.send(crate::core::AppCoreMessage::AgentResponse(format!("💭 {}", thinking))).await;
                            }
                            AgentUpdate::ConversationHistoryUpdated => {
                                // This could trigger a UI refresh if needed
                                // For now, we don't need to do anything special
                            }
                            AgentUpdate::WidgetGeneration(request) => {
                                // Forward widget generation request to UI
                                let _ = ui_tx.send(crate::core::AppCoreMessage::WidgetGeneration(request)).await;
                            }
                            AgentUpdate::WidgetUpdate(event) => {
                                // Forward widget update event to UI
                                let _ = ui_tx.send(crate::core::AppCoreMessage::WidgetUpdate(event)).await;
                            }
                            AgentUpdate::AutocompleteRequest(context) => {
                                // Handle autocomplete request
                                let _ = ui_tx.send(crate::core::AppCoreMessage::AgentResponse(format!("📝 Autocomplete: {} ({})", context.input_type, context.partial_input))).await;
                            }
                        }
                    }

                    else => break,
                }
            }

            info!("Agent task exiting");
        })
    }

}

/// Format tool output for display in the UI
fn format_tool_output(output: &Value) -> Option<String> {
    match output {
        Value::Object(map) => {
            // Special handling for common tool output patterns
            if let Some(content) = map.get("content") {
                if let Value::String(s) = content {
                    return Some(format!("Content: {}", s.trim()));
                }
            }
            
            if let Some(message) = map.get("message") {
                if let Value::String(s) = message {
                    return Some(s.clone());
                }
            }
            
            if let Some(result) = map.get("result") {
                return Some(format!("Result: {}", result));
            }
            
            // For file operations, show relevant info
            if let Some(file_path) = map.get("file_path") {
                if let Some(bytes_written) = map.get("bytes_written") {
                    return Some(format!("Written {} bytes to {}", bytes_written, file_path));
                }
                if let Some(lines_read) = map.get("lines_read") {
                    return Some(format!("Read {} lines from {}", lines_read, file_path));
                }
            }
            
            // For success/error objects
            if let Some(success) = map.get("success") {
                if success.as_bool() == Some(true) {
                    if let Some(content) = map.get("content") {
                        return Some(format!("Success: {}", content));
                    }
                    return Some("Success".to_string());
                }
            }
            
            if let Some(error) = map.get("error") {
                if error.as_bool() == Some(true) {
                    if let Some(message) = map.get("message") {
                        return Some(format!("Error: {}", message));
                    }
                    return Some("Error occurred".to_string());
                }
            }
            
            None
        }
        Value::String(s) => Some(s.clone()),
        Value::Number(n) => Some(n.to_string()),
        Value::Bool(b) => Some(b.to_string()),
        Value::Array(arr) => {
            if arr.len() <= 3 {
                Some(format!("Array[{}]: {:?}", arr.len(), arr))
            } else {
                Some(format!("Array[{}]: [first few items: {:?}...]", arr.len(), &arr[..3]))
            }
        }
        Value::Null => Some("null".to_string()),
    }
}
